# globalTower

Create a browser-based tower defense prototype using vanilla JavaScript, HTML5 Canvas, and optional CSS.

Requirements:
1. Grid-based map.
2. Enemies spawn at a start point and move to an end point using A* pathfinding.
3. Towers can be placed on empty tiles (not the path).
4. Allow user to place barriers (e.g. rocks) on the grid by clicking.
   - Barriers are impassable and force enemies to re-calculate paths.
   - Prevent placing barriers that block all paths.
5. Game loop using requestAnimationFrame.
6. Enemies:
   - Move along path toward goal.
   - Take damage from towers.
   - Are removed when health reaches 0.
7. Towers:
   - Have a range and fire rate.
   - Automatically shoot nearest enemy in range.
8. UI:
   - Show gold, lives, wave number.
   - Start next wave button.

File structure:
- index.html
- style.css (optional)
- game.js (main loop, input handling)
- grid.js (A* pathfinding, barrier logic)
- enemy.js
- tower.js

Keep it modular. No external libraries.
